# GraspLDM Simplified Inference Interface

This document describes the simplified inference interface for GraspLDM that bypasses the complex ACRONYM dataset structure and accepts raw point cloud data directly.

## Overview

The `SimpleGraspLDMInference` class provides a streamlined interface for real-time robotic applications where you have direct access to camera data rather than pre-structured datasets.

### Key Features

- ✅ **Direct Point Cloud Input**: Accept numpy arrays or torch tensors directly
- ✅ **Camera Pose Support**: Handle coordinate transformations with extrinsic parameters
- ✅ **Same Preprocessing**: Identical preprocessing pipeline as original implementation
- ✅ **Model Compatibility**: Works with existing model weights and configurations
- ✅ **Real-time Ready**: Optimized for robotic applications
- ✅ **Flexible Output**: Same output format as original inference pipeline

## Quick Start

### Basic Usage

```python
from tools.simple_inference import SimpleGraspLDMInference
import numpy as np

# Initialize inference engine
inference = SimpleGraspLDMInference(
    exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
    device="cuda:0"
)

# Your point cloud data (from camera, file, etc.)
pointcloud = np.random.randn(2000, 3) * 0.1  # Example: [N, 3] array

# Generate grasps
results = inference.infer_from_pointcloud(
    pointcloud=pointcloud,
    num_grasps=20,
    visualize=True
)

# Access results
grasps = results["grasps"]        # [20, 4, 4] transformation matrices
confidence = results["confidence"] # [20, 1] success probabilities
```

### With Camera Pose Transformation

```python
# Point cloud in camera coordinates
pc_camera = load_from_camera()  # [N, 3]

# Camera extrinsic matrix (camera to world transform)
camera_pose = np.array([
    [1, 0, 0, 0],
    [0, 1, 0, 0], 
    [0, 0, 1, 0.5],  # 50cm above origin
    [0, 0, 0, 1]
])

# Generate grasps with coordinate transformation
results = inference.infer_from_pointcloud(
    pointcloud=pc_camera,
    camera_pose=camera_pose,
    num_grasps=15
)
```

## API Reference

### SimpleGraspLDMInference

#### Constructor

```python
SimpleGraspLDMInference(
    exp_path: str,                          # Path to experiment directory
    device: str = "cuda:0",                 # Compute device
    use_ema_model: bool = True,             # Use EMA model weights
    use_fast_sampler: bool = True,          # Enable fast sampling (DDIM)
    num_inference_steps: int = 100,         # Number of denoising steps
    num_points: int = 1024,                 # Target point cloud size
)
```

#### Main Methods

##### `infer_from_pointcloud()`

```python
infer_from_pointcloud(
    pointcloud: Union[np.ndarray, torch.Tensor],  # [N, 3] point cloud
    camera_pose: Optional[np.ndarray] = None,     # [4, 4] transformation matrix
    num_grasps: int = 20,                         # Number of grasps to generate
    visualize: bool = False,                      # Show 3D visualization
    return_scene: bool = False                    # Return scene object
) -> Dict[str, torch.Tensor]
```

**Returns:**
- `grasps`: `[num_grasps, 4, 4]` homogeneous transformation matrices
- `confidence`: `[num_grasps, 1]` grasp success probabilities
- `pc`: `[1024, 3]` processed point cloud
- `qualities`: Optional quality metrics (if model supports)

##### Utility Methods

```python
# Load point cloud from file
pc = inference.load_pointcloud_from_file("data/object.ply")

# Filter by confidence threshold
good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.7)

# Get top-k grasps
top_grasps = inference.get_best_grasps(results, top_k=5)
```

## Input Requirements

### Point Cloud Format

- **Shape**: `[N, 3]` where N is the number of points
- **Type**: `numpy.ndarray` or `torch.Tensor`
- **Coordinates**: 3D coordinates (x, y, z) in meters
- **Range**: Typical object scale ~0.1-0.3m

### Camera Pose Format

- **Shape**: `[4, 4]` homogeneous transformation matrix
- **Type**: `numpy.ndarray` or `torch.Tensor`
- **Meaning**: Camera-to-world transformation
- **Units**: Translation in meters, rotation as rotation matrix

## Preprocessing Pipeline

The simplified interface applies the same preprocessing as the original dataset:

1. **Point Cloud Preparation**:
   - Convert to torch tensor
   - Apply camera pose transformation (if provided)
   - Regularize to 1024 points (upsample/downsample)

2. **Centering**:
   - Compute point cloud centroid
   - Center point cloud on origin

3. **Normalization**:
   - Apply dataset statistics normalization
   - PC scale: 0.05, Grasp translation scale: 0.05, Grasp rotation scale: 0.5

## Examples

### Example 1: Basic Sphere

```python
# Create synthetic sphere
theta = np.random.uniform(0, 2*np.pi, 2000)
phi = np.random.uniform(0, np.pi, 2000)
r = 0.1

x = r * np.sin(phi) * np.cos(theta)
y = r * np.sin(phi) * np.sin(theta)
z = r * np.cos(phi)

pc = np.column_stack([x, y, z])

# Generate grasps
results = inference.infer_from_pointcloud(pc, num_grasps=20)
```

### Example 2: From File

```python
# Load from PLY file
pc = inference.load_pointcloud_from_file("data/mug.ply")

# Generate and filter grasps
results = inference.infer_from_pointcloud(pc, num_grasps=30)
good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.8)
```

### Example 3: Real-time Camera

```python
import open3d as o3d

# Capture from camera (pseudo-code)
def capture_pointcloud():
    # Your camera capture logic here
    return pc_array, camera_extrinsics

# Real-time inference loop
while True:
    pc, cam_pose = capture_pointcloud()
    
    results = inference.infer_from_pointcloud(
        pointcloud=pc,
        camera_pose=cam_pose,
        num_grasps=10
    )
    
    # Execute best grasp
    best_grasp = inference.get_best_grasps(results, top_k=1)
    execute_grasp(best_grasp["grasps"][0])
```

## Performance Tips

1. **Use Fast Sampling**: Enable `use_fast_sampler=True` for 10x speedup
2. **Reduce Inference Steps**: Use 50-100 steps instead of 1000 for faster inference
3. **GPU Memory**: Use smaller batch sizes if running out of memory
4. **Point Cloud Size**: 1024 points is optimal; more points don't improve quality significantly

## Troubleshooting

### Common Issues

1. **Model Loading Errors**: Ensure experiment path contains both `ddm/` and `vae/` subdirectories
2. **CUDA Errors**: Check GPU memory usage and reduce `num_grasps` if needed
3. **Point Cloud Shape**: Ensure input is `[N, 3]` format
4. **Coordinate Systems**: Verify camera pose transformation is correct

### Debug Mode

```python
# Enable verbose output
inference = SimpleGraspLDMInference(exp_path, device="cuda:0")

# Check preprocessing
pc_prep = inference._prepare_pointcloud(your_pc)
print(f"Prepared PC shape: {pc_prep.shape}")

# Check normalization
pc_norm, metas = inference._preprocess_pointcloud(pc_prep)
print(f"Normalization metadata: {metas}")
```

## Integration with ROS

```python
import rospy
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseArray

class GraspLDMNode:
    def __init__(self):
        self.inference = SimpleGraspLDMInference(exp_path)
        self.pc_sub = rospy.Subscriber("/camera/points", PointCloud2, self.pc_callback)
        self.grasp_pub = rospy.Publisher("/grasps", PoseArray, queue_size=1)
    
    def pc_callback(self, msg):
        # Convert ROS PointCloud2 to numpy
        pc = pointcloud2_to_array(msg)
        
        # Generate grasps
        results = self.inference.infer_from_pointcloud(pc, num_grasps=10)
        
        # Publish as ROS poses
        self.publish_grasps(results["grasps"], results["confidence"])
```

For complete examples, see `examples/simple_inference_example.py`.
