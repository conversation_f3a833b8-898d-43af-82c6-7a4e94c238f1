"""
Grasp Generation Script for GraspLDM

This script provides functionality to generate robotic grasps using either Variational Autoencoder (VAE) 
or Latent Diffusion Model (LDM) approaches. It supports various conditioning modes including unconditional,
class-conditioned, and region-conditioned grasp generation.

The script can process multiple object samples from the ACRONYM dataset and optionally visualize
the generated grasps in 3D space.

Usage:
    python generate_grasps.py --exp_path /path/to/experiment --mode LDM --num_grasps 20 --visualize

"""

import argparse
import os
import sys
from typing import Optional, Tuple

import numpy as np

# Set environment variable for OpenGL to prevent potential issues in headless environments.
# This is particularly important when running on servers without display capabilities.
os.environ["LIBGL_ALWAYS_INDIRECT"] = "0"

# Add the current working directory to the system path to allow for package imports.
# This ensures that local modules can be imported regardless of the script's execution location.
sys.path.append((os.getcwd()))

from tools.inference import Conditioning, InferenceLDM, InferenceVAE, ModelType


def parse_args():
    """
    Parses command-line arguments for the grasp generation script.
    
    This function defines all available command-line options including model configuration,
    dataset parameters, generation settings, and visualization options.
    
    Returns:
        argparse.Namespace: Parsed command-line arguments containing all configuration parameters.
    """
    parser = argparse.ArgumentParser(description="Grasp Generation Script")
    
    # Model and experiment configuration
    parser.add_argument(
        "--exp_path", 
        type=str, 
        required=True, 
        help="Path to the root directory of a trained model experiment."
    )
    parser.add_argument(
        "--data_root", 
        type=str, 
        default="data/ACRONYM", 
        help="Root directory for the ACRONYM dataset."
    )
    parser.add_argument(
        "--mode",
        type=str,
        choices=["VAE", "LDM"],
        default="VAE",
        help="Specifies the model type to use for inference (VAE or LDM).",
    )
    
    # Dataset configuration
    parser.add_argument(
        "--split", 
        type=str, 
        default="test", 
        help="Dataset split to use for inference (e.g., 'train', 'test')."
    )
    parser.add_argument(
        "--num_samples", 
        type=int, 
        default=11, 
        help="Number of different object samples to process from the dataset."
    )
    
    # Generation parameters
    parser.add_argument(
        "--num_grasps", 
        type=int, 
        default=20, 
        help="Number of grasps to generate per object."
    )
    parser.add_argument(
        "--inference_steps",
        type=int,
        default=100,
        help="Number of denoising steps for LDM inference.",
    )
    
    # Conditioning options
    parser.add_argument(
        "--conditioning",
        type=str,
        choices=["unconditional", "class", "region"],
        default="unconditional",
        help="Type of conditioning to apply during grasp generation.",
    )
    parser.add_argument(
        "--condition_value",
        type=int,
        help="The specific value for conditioning (e.g., a class label or a region ID). Required if conditioning is not 'unconditional'.",
    )
    
    # Model and visualization options
    parser.add_argument(
        "--visualize", 
        action="store_true", 
        help="If set, enables 3D visualization of the generated grasps."
    )
    parser.add_argument(
        "--no_ema",
        action="store_false",
        dest="use_ema_model",
        help="If set, disables the use of Exponential Moving Average (EMA) model weights.",
    )
    
    return parser.parse_args()


def setup_model(args):
    """
    Initializes and configures the inference model (VAE or LDM) based on provided arguments.
    
    This function handles the instantiation of either a Latent Diffusion Model or Variational
    Autoencoder based on the specified mode. It extracts experiment information from the
    provided path and configures the model with appropriate parameters.

    Args:
        args (argparse.Namespace): Parsed command-line arguments containing model configuration.

    Returns:
        Union[InferenceLDM, InferenceVAE]: An instance of the configured inference model.
        
    Raises:
        ValueError: If an unsupported model mode is specified.
    """
    # Extract experiment name and root directory from the provided path.
    # This allows for flexible experiment organization and path handling.
    exp_name = os.path.basename(args.exp_path)
    exp_out_root = os.path.dirname(args.exp_path)

    # Instantiate the appropriate inference class based on the selected mode.
    if args.mode == "LDM":
        # Initialize Latent Diffusion Model for grasp generation
        model = InferenceLDM(
            exp_name=exp_name,
            exp_out_root=exp_out_root,
            use_elucidated=False,  # Use standard diffusion formulation
            data_root=args.data_root,
            load_dataset=True,  # Load dataset for inference
            num_inference_steps=args.inference_steps,  # Number of denoising steps
            use_fast_sampler=False,  # Use standard DDPM sampler
            data_split=args.split,
            use_ema_model=args.use_ema_model,  # Use EMA weights if available
        )
        # For debugging and verification, print the noise schedule used during training.
        # This helps ensure consistency between training and inference configurations.
        print(
            f"Trained using noise schedule: beta0 = {model.model.diffusion_model.beta_start} ; betaT = {model.model.diffusion_model.beta_end}"
        )
    elif args.mode == "VAE":
        # Initialize Variational Autoencoder for grasp generation
        model = InferenceVAE(
            exp_name=exp_name,
            exp_out_root=exp_out_root,
            data_root=args.data_root,
            load_dataset=True,  # Load dataset for inference
            data_split=args.split,
            use_ema_model=args.use_ema_model,  # Use EMA weights if available
        )
    else:
        raise ValueError(f"Unsupported model mode: {args.mode}")
    
    return model


def get_conditioning(args) -> Tuple[Optional[Conditioning], Optional[int]]:
    """
    Parses conditioning arguments and returns the corresponding Conditioning enum and value.
    
    This function validates the conditioning configuration and ensures that required
    condition values are provided when needed. It supports three types of conditioning:
    unconditional, class-conditioned, and region-conditioned generation.

    Args:
        args (argparse.Namespace): Parsed command-line arguments containing conditioning configuration.

    Returns:
        Tuple[Optional[Conditioning], Optional[int]]: A tuple containing:
            - Conditioning enum type (or None for unconditional)
            - Integer value for conditioning (or None if not applicable)
            
    Raises:
        ValueError: If condition_value is not provided when required for conditional generation.
    """
    if args.conditioning == "unconditional":
        # No conditioning applied - generate grasps without any constraints
        return Conditioning.UNCONDITIONAL, None
    elif args.conditioning == "class":
        # Class-conditioned generation - generate grasps for specific object classes
        if args.condition_value is None:
            raise ValueError("Must provide --condition_value for class conditioning")
        return Conditioning.CLASS_CONDITIONED, args.condition_value
    elif args.conditioning == "region":
        # Region-conditioned generation - generate grasps for specific object regions
        if args.condition_value is None:
            raise ValueError("Must provide --condition_value for region conditioning")
        return Conditioning.REGION_CONDITIONED, args.condition_value
    
    # This should never be reached due to argparse choices validation
    return None, None


def main():
    """
    Main function to run the grasp generation pipeline.
    
    This function orchestrates the entire grasp generation process:
    1. Parse command-line arguments
    2. Initialize the inference model
    3. Configure conditioning parameters
    4. Generate grasps for multiple object samples
    5. Optionally visualize results
    
    The function processes a specified number of random samples from the dataset
    and generates the requested number of grasps for each sample.
    """
    # Parse command-line arguments to get configuration parameters
    args = parse_args()
    
    # Set up the inference model based on the specified mode (VAE or LDM)
    model = setup_model(args)
    
    # Determine the conditioning type and value based on user input
    condition_type, conditioning = get_conditioning(args)

    print(f"Starting grasp generation for {args.num_samples} samples...")
    print(f"Model type: {args.mode}")
    print(f"Conditioning: {args.conditioning}")
    print(f"Grasps per object: {args.num_grasps}")
    
    # Loop to generate grasps for a specified number of random samples.
    # Each iteration processes a different object from the dataset.
    for sample_idx in range(args.num_samples):
        print(f"\nProcessing sample {sample_idx + 1}/{args.num_samples}")
        
        # Select a random object sample from the dataset.
        # This ensures diverse object coverage across multiple runs.
        data_idx = np.random.randint(0, len(model.dataset))
        print(f"Selected dataset index: {data_idx}")

        # VAE model only supports unconditional generation in this script.
        # Override conditioning settings for VAE to prevent errors.
        if args.mode == "VAE":
            condition_type = Conditioning.UNCONDITIONAL
            conditioning = None
            if args.conditioning != "unconditional":
                print("Warning: VAE mode only supports unconditional generation. Overriding conditioning settings.")

        # Run inference to generate grasps for the selected object.
        # This is the core computation step that produces grasp candidates.
        print(f"Generating {args.num_grasps} grasps...")
        results = model.infer(
            data_idx=data_idx,
            num_grasps=args.num_grasps,
            visualize=args.visualize,
            condition_type=condition_type,
            conditioning=conditioning,
        )

        # If visualization is enabled, show the resulting scene.
        # This provides immediate visual feedback on the generated grasps.
        if args.visualize:
            print("Displaying visualization...")
            results.show(line_settings={"point_size": 10})
        
        print(f"Completed sample {sample_idx + 1}")
    
    print(f"\nGrasp generation completed for all {args.num_samples} samples!")


if __name__ == "__main__":
    # Entry point of the script.
    # This ensures the main function only runs when the script is executed directly,
    # not when imported as a module.
    main()
