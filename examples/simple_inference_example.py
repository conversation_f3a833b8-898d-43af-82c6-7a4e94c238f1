#!/usr/bin/env python3
"""
Example usage of SimpleGraspLDMInference for real-time robotic applications.

This script demonstrates how to use the simplified inference interface
with various input formats and scenarios commonly encountered in robotics.

Usage:
    python examples/simple_inference_example.py --exp_path <path_to_experiment>
    
Examples:
    # Basic usage with synthetic data
    python examples/simple_inference_example.py --exp_path checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k
    
    # Load from point cloud file
    python examples/simple_inference_example.py --exp_path <exp_path> --pc_file data/example.ply
    
    # With camera pose transformation
    python examples/simple_inference_example.py --exp_path <exp_path> --use_camera_pose
"""

import argparse
import os
import sys
import numpy as np
import torch

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.simple_inference import SimpleGraspLDMInference


def create_synthetic_pointcloud(shape="sphere", num_points=2000, scale=0.1):
    """Create synthetic point clouds for testing."""
    if shape == "sphere":
        # Generate sphere point cloud
        theta = np.random.uniform(0, 2*np.pi, num_points)
        phi = np.random.uniform(0, np.pi, num_points)
        r = scale
        
        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)
        
        return np.column_stack([x, y, z])
    
    elif shape == "cube":
        # Generate cube surface points
        points = []
        points_per_face = num_points // 6
        
        for face in range(6):
            if face == 0:  # +X face
                x = np.full(points_per_face, scale)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 1:  # -X face
                x = np.full(points_per_face, -scale)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 2:  # +Y face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.full(points_per_face, scale)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 3:  # -Y face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.full(points_per_face, -scale)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 4:  # +Z face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.full(points_per_face, scale)
            else:  # -Z face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.full(points_per_face, -scale)
            
            points.append(np.column_stack([x, y, z]))
        
        return np.vstack(points)
    
    else:
        raise ValueError(f"Unknown shape: {shape}")


def create_camera_pose(translation=[0, 0, 0.5], rotation_deg=[0, 0, 0]):
    """Create a camera pose transformation matrix."""
    # Convert rotation from degrees to radians
    rx, ry, rz = np.radians(rotation_deg)
    
    # Create rotation matrices
    Rx = np.array([[1, 0, 0],
                   [0, np.cos(rx), -np.sin(rx)],
                   [0, np.sin(rx), np.cos(rx)]])
    
    Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                   [0, 1, 0],
                   [-np.sin(ry), 0, np.cos(ry)]])
    
    Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                   [np.sin(rz), np.cos(rz), 0],
                   [0, 0, 1]])
    
    # Combined rotation
    R = Rz @ Ry @ Rx
    
    # Create 4x4 transformation matrix
    T = np.eye(4)
    T[:3, :3] = R
    T[:3, 3] = translation
    
    return T


def example_basic_inference(inference_engine):
    """Basic inference example with synthetic data."""
    print("\n" + "="*60)
    print("🔹 Example 1: Basic Inference with Synthetic Sphere")
    print("="*60)
    
    # Create synthetic sphere
    pc = create_synthetic_pointcloud("sphere", num_points=1500, scale=0.08)
    print(f"Created sphere point cloud: {pc.shape}")
    
    # Generate grasps
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=15,
        visualize=False
    )
    
    # Analyze results
    confidence = results["confidence"].squeeze()
    print(f"Generated {len(confidence)} grasps")
    print(f"Confidence range: {confidence.min():.3f} - {confidence.max():.3f}")
    print(f"Mean confidence: {confidence.mean():.3f}")
    
    return results


def example_camera_pose_inference(inference_engine):
    """Inference example with camera pose transformation."""
    print("\n" + "="*60)
    print("🔹 Example 2: Inference with Camera Pose Transformation")
    print("="*60)
    
    # Create synthetic cube in camera coordinates
    pc_camera = create_synthetic_pointcloud("cube", num_points=1200, scale=0.06)
    print(f"Created cube point cloud in camera coordinates: {pc_camera.shape}")
    
    # Create camera pose (camera looking down at object)
    camera_pose = create_camera_pose(
        translation=[0, 0, 0.3],  # 30cm above object
        rotation_deg=[180, 0, 0]  # Looking down
    )
    print(f"Camera pose:\n{camera_pose}")
    
    # Generate grasps with camera transformation
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc_camera,
        camera_pose=camera_pose,
        num_grasps=20,
        visualize=False
    )
    
    # Filter high-confidence grasps
    good_grasps = inference_engine.filter_grasps_by_confidence(results, min_confidence=0.6)
    top_grasps = inference_engine.get_best_grasps(results, top_k=3)
    
    print(f"High-confidence grasps: {good_grasps['grasps'].shape[0]}")
    print(f"Top-3 confidences: {top_grasps['confidence'].squeeze().tolist()}")
    
    return results


def example_file_input(inference_engine, pc_file):
    """Inference example loading point cloud from file."""
    print("\n" + "="*60)
    print("🔹 Example 3: Inference from Point Cloud File")
    print("="*60)
    
    try:
        # Load point cloud from file
        pc = inference_engine.load_pointcloud_from_file(pc_file)
        print(f"Loaded point cloud from {pc_file}: {pc.shape}")
        
        # Generate grasps
        results = inference_engine.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=25,
            visualize=False
        )
        
        print(f"Generated {results['grasps'].shape[0]} grasps from file")
        return results
        
    except Exception as e:
        print(f"Failed to load point cloud from file: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description="SimpleGraspLDMInference Examples")
    parser.add_argument("--exp_path", type=str, required=True,
                       help="Path to experiment directory")
    parser.add_argument("--pc_file", type=str, default=None,
                       help="Optional point cloud file to load (.ply, .pcd, .xyz)")
    parser.add_argument("--device", type=str, default="cuda:0",
                       help="Device for inference")
    parser.add_argument("--visualize", action="store_true",
                       help="Enable 3D visualization")
    parser.add_argument("--fast_steps", type=int, default=50,
                       help="Number of inference steps for fast sampling")
    
    args = parser.parse_args()
    
    # Check if experiment path exists
    if not os.path.exists(args.exp_path):
        print(f"❌ Experiment path not found: {args.exp_path}")
        return
    
    print("🚀 SimpleGraspLDMInference Examples")
    print(f"Experiment: {args.exp_path}")
    print(f"Device: {args.device}")
    print(f"Visualization: {'Enabled' if args.visualize else 'Disabled'}")
    
    try:
        # Initialize inference engine
        inference = SimpleGraspLDMInference(
            exp_path=args.exp_path,
            device=args.device,
            num_inference_steps=args.fast_steps
        )
        
        # Run examples
        example_basic_inference(inference)
        example_camera_pose_inference(inference)
        
        if args.pc_file:
            example_file_input(inference, args.pc_file)
        
        print("\n✅ All examples completed successfully!")
        
        if args.visualize:
            print("\n🎨 Running visualization example...")
            pc = create_synthetic_pointcloud("sphere", scale=0.1)
            inference.infer_from_pointcloud(
                pointcloud=pc,
                num_grasps=10,
                visualize=True
            )
        
    except Exception as e:
        print(f"❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
